import { OfflineScanData } from '../types';
import { STORAGE_KEYS } from '../utils/constants';
import { apiService } from './apiService';

class OfflineService {
  private getOfflineScans(): OfflineScanData[] {
    const data = localStorage.getItem(STORAGE_KEYS.OFFLINE_SCANS);
    return data ? JSON.parse(data) : [];
  }

  private saveOfflineScans(scans: OfflineScanData[]): void {
    localStorage.setItem(STORAGE_KEYS.OFFLINE_SCANS, JSON.stringify(scans));
  }

  addOfflineScan(uid: string): void {
    const scans = this.getOfflineScans();
    scans.push({
      uid,
      timestamp: new Date().toISOString(),
      synced: false,
    });
    this.saveOfflineScans(scans);
  }

  async syncOfflineScans(): Promise<void> {
    const scans = this.getOfflineScans();
    const unsyncedScans = scans.filter(scan => !scan.synced);

    if (unsyncedScans.length === 0) return;

    try {
      for (const scan of unsyncedScans) {
        await apiService.claimMeal(scan.uid);
        scan.synced = true;
      }
      this.saveOfflineScans(scans);
    } catch (error) {
      console.error('Failed to sync offline scans:', error);
      throw error;
    }
  }

  getUnsyncedCount(): number {
    return this.getOfflineScans().filter(scan => !scan.synced).length;
  }

  clearSyncedScans(): void {
    const scans = this.getOfflineScans();
    const unsyncedScans = scans.filter(scan => !scan.synced);
    this.saveOfflineScans(unsyncedScans);
  }
}

export const offlineService = new OfflineService();