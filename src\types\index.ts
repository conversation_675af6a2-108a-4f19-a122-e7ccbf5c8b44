export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'scanner';
  createdAt: string;
}

export interface Attendee {
  id: string;
  uid: string;
  name: string;
  email: string;
  photo?: string;
  mealClaimed: boolean;
  claimedAt?: string;
  claimedBy?: string;
  createdAt: string;
}

export interface MealType {
  id: string;
  name: string;
  description: string;
  portionLimit: number;
  isActive: boolean;
  createdAt: string;
}

export interface ScanResult {
  success: boolean;
  status: 'eligible' | 'already_claimed' | 'not_found' | 'error';
  attendee?: Attendee;
  message: string;
  color: 'green' | 'red' | 'gray';
}

export interface AuthContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  loading: boolean;
}

export interface OfflineScanData {
  uid: string;
  timestamp: string;
  synced: boolean;
}

export interface DashboardStats {
  totalAttendees: number;
  mealsServed: number;
  remainingMeals: number;
  scanRate: number;
  recentScans: Array<{
    attendeeName: string;
    timestamp: string;
    status: string;
  }>;
}