import React, { useEffect, useState } from 'react';
import { apiService } from '../../services/apiService';
import { Attendee } from '../../types';
import { Button } from '../common/Button';
import { Input } from '../common/Input';
import { Plus, Search, Edit, Trash2, User, QrCode } from 'lucide-react';
import { formatDate, generateUID } from '../../utils/helpers';

export function AttendeeManagement() {
  const [attendees, setAttendees] = useState<Attendee[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingAttendee, setEditingAttendee] = useState<Attendee | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    uid: ''
  });

  useEffect(() => {
    loadAttendees();
  }, []);

  const loadAttendees = async () => {
    try {
      const data = await apiService.getAttendees();
      setAttendees(data);
    } catch (error) {
      console.error('Failed to load attendees:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (editingAttendee) {
        await apiService.updateAttendee(editingAttendee.id, formData);
      } else {
        await apiService.createAttendee({
          ...formData,
          mealClaimed: false
        });
      }
      
      await loadAttendees();
      resetForm();
    } catch (error) {
      console.error('Failed to save attendee:', error);
    }
  };

  const handleEdit = (attendee: Attendee) => {
    setEditingAttendee(attendee);
    setFormData({
      name: attendee.name,
      email: attendee.email,
      uid: attendee.uid
    });
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this attendee?')) return;
    
    try {
      await apiService.deleteAttendee(id);
      await loadAttendees();
    } catch (error) {
      console.error('Failed to delete attendee:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      uid: generateUID()
    });
    setEditingAttendee(null);
    setShowForm(false);
  };

  const generateQRCode = (uid: string) => {
    // In a real implementation, this would generate a QR code image
    // For demo purposes, we'll show the UID
    alert(`QR Code for UID: ${uid}\n\nIn production, this would generate a downloadable QR code image.`);
  };

  const filteredAttendees = attendees.filter(attendee =>
    attendee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    attendee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    attendee.uid.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Attendee Management
        </h1>
        <Button
          onClick={() => {
            setFormData({
              name: '',
              email: '',
              uid: generateUID()
            });
            setShowForm(true);
          }}
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Attendee
        </Button>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <Input
          type="text"
          placeholder="Search attendees..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              {editingAttendee ? 'Edit Attendee' : 'Add New Attendee'}
            </h2>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                label="Full Name"
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
              
              <Input
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                required
              />
              
              <Input
                label="UID"
                type="text"
                value={formData.uid}
                onChange={(e) => setFormData({ ...formData, uid: e.target.value })}
                required
                helperText="Unique identifier for QR code"
              />
              
              <div className="flex space-x-3 pt-4">
                <Button type="submit" className="flex-1">
                  {editingAttendee ? 'Update' : 'Create'}
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={resetForm}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Attendees List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Attendee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  UID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredAttendees.map((attendee) => (
                <tr key={attendee.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                          <User className="w-5 h-5 text-blue-600 dark:text-blue-300" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {attendee.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {attendee.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <code className="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                      {attendee.uid}
                    </code>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 text-xs font-semibold rounded-full ${
                      attendee.mealClaimed
                        ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                        : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                    }`}>
                      {attendee.mealClaimed ? 'Meal Claimed' : 'Eligible'}
                    </span>
                    {attendee.claimedAt && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {formatDate(attendee.claimedAt)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatDate(attendee.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm space-x-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => generateQRCode(attendee.uid)}
                    >
                      <QrCode className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleEdit(attendee)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDelete(attendee.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredAttendees.length === 0 && (
          <div className="text-center py-12">
            <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm ? 'No attendees found matching your search.' : 'No attendees yet.'}
            </p>
            {!searchTerm && (
              <Button
                onClick={() => {
                  setFormData({
                    name: '',
                    email: '',
                    uid: generateUID()
                  });
                  setShowForm(true);
                }}
                className="mt-4"
              >
                Add First Attendee
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}