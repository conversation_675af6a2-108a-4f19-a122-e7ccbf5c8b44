const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.PORT || 3001;
const JWT_SECRET = process.env.JWT_SECRET || 'kyubiscan-jwt-secret-key';

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Database setup
const dbPath = path.join(__dirname, 'database.sqlite');
const db = new sqlite3.Database(dbPath);

// Initialize database
db.serialize(() => {
  // Users table
  db.run(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      username TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      role TEXT NOT NULL CHECK (role IN ('admin', 'scanner')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Attendees table
  db.run(`
    CREATE TABLE IF NOT EXISTS attendees (
      id TEXT PRIMARY KEY,
      uid TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      email TEXT NOT NULL,
      photo TEXT,
      meal_claimed BOOLEAN DEFAULT FALSE,
      claimed_at DATETIME,
      claimed_by TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (claimed_by) REFERENCES users(id)
    )
  `);

  // Meal types table
  db.run(`
    CREATE TABLE IF NOT EXISTS meal_types (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      portion_limit INTEGER DEFAULT 1,
      is_active BOOLEAN DEFAULT TRUE,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Scan logs table
  db.run(`
    CREATE TABLE IF NOT EXISTS scan_logs (
      id TEXT PRIMARY KEY,
      attendee_id TEXT,
      scanner_id TEXT NOT NULL,
      status TEXT NOT NULL,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (attendee_id) REFERENCES attendees(id),
      FOREIGN KEY (scanner_id) REFERENCES users(id)
    )
  `);

  // Create default users
  const adminPassword = bcrypt.hashSync('admin123', 10);
  const scannerPassword = bcrypt.hashSync('scanner123', 10);

  db.run(`
    INSERT OR IGNORE INTO users (id, username, email, password, role)
    VALUES 
      ('admin-1', 'admin', '<EMAIL>', ?, 'admin'),
      ('scanner-1', 'scanner', '<EMAIL>', ?, 'scanner')
  `, [adminPassword, scannerPassword]);

  // Create sample attendees
  const sampleAttendees = [
    { uid: 'ATT-DEMO001', name: 'John Smith', email: '<EMAIL>' },
    { uid: 'ATT-DEMO002', name: 'Sarah Johnson', email: '<EMAIL>' },
    { uid: 'ATT-DEMO003', name: 'Mike Davis', email: '<EMAIL>' },
    { uid: 'ATT-DEMO004', name: 'Emily Brown', email: '<EMAIL>' },
    { uid: 'ATT-DEMO005', name: 'Chris Wilson', email: '<EMAIL>' }
  ];

  sampleAttendees.forEach(attendee => {
    db.run(`
      INSERT OR IGNORE INTO attendees (id, uid, name, email)
      VALUES (?, ?, ?, ?)
    `, [uuidv4(), attendee.uid, attendee.name, attendee.email]);
  });

  // Create default meal type
  db.run(`
    INSERT OR IGNORE INTO meal_types (id, name, description)
    VALUES ('meal-1', 'Standard Meal', 'Default event meal package')
  `);
});

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

// Role-based authorization middleware
const requireRole = (role) => {
  return (req, res, next) => {
    if (req.user.role !== role) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};

// Auth routes
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;

  db.get(
    'SELECT * FROM users WHERE username = ?',
    [username],
    (err, user) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (!user || !bcrypt.compareSync(password, user.password)) {
        return res.status(401).json({ error: 'Invalid credentials' });
      }

      const token = jwt.sign(
        { id: user.id, username: user.username, role: user.role },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      res.json({
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          createdAt: user.created_at
        }
      });
    }
  );
});

// Attendee routes
app.get('/api/attendees', authenticateToken, (req, res) => {
  db.all(
    `SELECT 
      id, uid, name, email, photo, meal_claimed as mealClaimed,
      claimed_at as claimedAt, claimed_by as claimedBy, created_at as createdAt
    FROM attendees ORDER BY created_at DESC`,
    (err, rows) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(rows);
    }
  );
});

app.post('/api/attendees', authenticateToken, requireRole('admin'), (req, res) => {
  const { uid, name, email, photo } = req.body;
  const id = uuidv4();

  db.run(
    'INSERT INTO attendees (id, uid, name, email, photo) VALUES (?, ?, ?, ?, ?)',
    [id, uid, name, email, photo],
    function(err) {
      if (err) {
        if (err.message.includes('UNIQUE constraint failed')) {
          return res.status(409).json({ error: 'UID already exists' });
        }
        return res.status(500).json({ error: 'Database error' });
      }

      db.get(
        `SELECT 
          id, uid, name, email, photo, meal_claimed as mealClaimed,
          claimed_at as claimedAt, claimed_by as claimedBy, created_at as createdAt
        FROM attendees WHERE id = ?`,
        [id],
        (err, row) => {
          if (err) {
            return res.status(500).json({ error: 'Database error' });
          }
          res.status(201).json(row);
        }
      );
    }
  );
});

app.put('/api/attendees/:id', authenticateToken, requireRole('admin'), (req, res) => {
  const { id } = req.params;
  const { uid, name, email, photo } = req.body;

  db.run(
    'UPDATE attendees SET uid = ?, name = ?, email = ?, photo = ? WHERE id = ?',
    [uid, name, email, photo, id],
    function(err) {
      if (err) {
        if (err.message.includes('UNIQUE constraint failed')) {
          return res.status(409).json({ error: 'UID already exists' });
        }
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Attendee not found' });
      }

      db.get(
        `SELECT 
          id, uid, name, email, photo, meal_claimed as mealClaimed,
          claimed_at as claimedAt, claimed_by as claimedBy, created_at as createdAt
        FROM attendees WHERE id = ?`,
        [id],
        (err, row) => {
          if (err) {
            return res.status(500).json({ error: 'Database error' });
          }
          res.json(row);
        }
      );
    }
  );
});

app.delete('/api/attendees/:id', authenticateToken, requireRole('admin'), (req, res) => {
  const { id } = req.params;

  db.run('DELETE FROM attendees WHERE id = ?', [id], function(err) {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Attendee not found' });
    }

    res.json({ message: 'Attendee deleted successfully' });
  });
});

// Scanning routes
app.post('/api/scan', authenticateToken, (req, res) => {
  const { uid } = req.body;

  db.get(
    `SELECT 
      id, uid, name, email, photo, meal_claimed as mealClaimed,
      claimed_at as claimedAt, claimed_by as claimedBy, created_at as createdAt
    FROM attendees WHERE uid = ?`,
    [uid],
    (err, attendee) => {
      if (err) {
        return res.status(500).json({ 
          success: false, 
          status: 'error', 
          message: 'Database error',
          color: 'red'
        });
      }

      if (!attendee) {
        // Log the scan attempt
        db.run(
          'INSERT INTO scan_logs (id, scanner_id, status) VALUES (?, ?, ?)',
          [uuidv4(), req.user.id, 'not_found']
        );

        return res.json({
          success: false,
          status: 'not_found',
          message: 'Attendee not found in database',
          color: 'gray'
        });
      }

      // Log the scan
      db.run(
        'INSERT INTO scan_logs (id, attendee_id, scanner_id, status) VALUES (?, ?, ?, ?)',
        [uuidv4(), attendee.id, req.user.id, attendee.mealClaimed ? 'already_claimed' : 'eligible']
      );

      if (attendee.mealClaimed) {
        return res.json({
          success: false,
          status: 'already_claimed',
          message: 'Meal already claimed',
          color: 'red',
          attendee
        });
      }

      res.json({
        success: true,
        status: 'eligible',
        message: 'Attendee eligible for meal',
        color: 'green',
        attendee
      });
    }
  );
});

app.post('/api/scan/claim', authenticateToken, (req, res) => {
  const { uid } = req.body;

  db.get('SELECT * FROM attendees WHERE uid = ?', [uid], (err, attendee) => {
    if (err) {
      return res.status(500).json({ 
        success: false, 
        status: 'error', 
        message: 'Database error',
        color: 'red'
      });
    }

    if (!attendee) {
      return res.json({
        success: false,
        status: 'not_found',
        message: 'Attendee not found',
        color: 'gray'
      });
    }

    if (attendee.meal_claimed) {
      return res.json({
        success: false,
        status: 'already_claimed',
        message: 'Meal already claimed',
        color: 'red',
        attendee: {
          id: attendee.id,
          uid: attendee.uid,
          name: attendee.name,
          email: attendee.email,
          photo: attendee.photo,
          mealClaimed: attendee.meal_claimed,
          claimedAt: attendee.claimed_at,
          claimedBy: attendee.claimed_by,
          createdAt: attendee.created_at
        }
      });
    }

    // Claim the meal
    db.run(
      'UPDATE attendees SET meal_claimed = ?, claimed_at = CURRENT_TIMESTAMP, claimed_by = ? WHERE uid = ?',
      [true, req.user.id, uid],
      function(err) {
        if (err) {
          return res.status(500).json({ 
            success: false, 
            status: 'error', 
            message: 'Failed to claim meal',
            color: 'red'
          });
        }

        // Get updated attendee data
        db.get(
          `SELECT 
            id, uid, name, email, photo, meal_claimed as mealClaimed,
            claimed_at as claimedAt, claimed_by as claimedBy, created_at as createdAt
          FROM attendees WHERE uid = ?`,
          [uid],
          (err, updatedAttendee) => {
            if (err) {
              return res.status(500).json({ 
                success: false, 
                status: 'error', 
                message: 'Database error',
                color: 'red'
              });
            }

            // Log successful claim
            db.run(
              'INSERT INTO scan_logs (id, attendee_id, scanner_id, status) VALUES (?, ?, ?, ?)',
              [uuidv4(), updatedAttendee.id, req.user.id, 'meal_claimed']
            );

            res.json({
              success: true,
              status: 'eligible',
              message: 'Meal claimed successfully!',
              color: 'green',
              attendee: updatedAttendee
            });
          }
        );
      }
    );
  });
});

// Dashboard routes
app.get('/api/dashboard/stats', authenticateToken, (req, res) => {
  // Get total attendees
  db.get('SELECT COUNT(*) as total FROM attendees', (err, totalResult) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }

    // Get meals served
    db.get('SELECT COUNT(*) as served FROM attendees WHERE meal_claimed = ?', [true], (err, servedResult) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      // Get recent scans
      db.all(`
        SELECT 
          a.name as attendeeName,
          sl.status,
          sl.timestamp
        FROM scan_logs sl
        LEFT JOIN attendees a ON sl.attendee_id = a.id
        ORDER BY sl.timestamp DESC
        LIMIT 10
      `, (err, recentScans) => {
        if (err) {
          return res.status(500).json({ error: 'Database error' });
        }

        const totalAttendees = totalResult.total;
        const mealsServed = servedResult.served;
        const remainingMeals = totalAttendees - mealsServed;

        res.json({
          totalAttendees,
          mealsServed,
          remainingMeals,
          scanRate: totalAttendees > 0 ? Math.round((mealsServed / totalAttendees) * 100) : 0,
          recentScans: recentScans.map(scan => ({
            attendeeName: scan.attendeeName || 'Unknown',
            status: scan.status,
            timestamp: scan.timestamp
          }))
        });
      });
    });
  });
});

// Meal types routes
app.get('/api/meal-types', authenticateToken, (req, res) => {
  db.all(
    `SELECT 
      id, name, description, portion_limit as portionLimit,
      is_active as isActive, created_at as createdAt
    FROM meal_types ORDER BY created_at DESC`,
    (err, rows) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(rows);
    }
  );
});

app.post('/api/meal-types', authenticateToken, requireRole('admin'), (req, res) => {
  const { name, description, portionLimit, isActive } = req.body;
  const id = uuidv4();

  db.run(
    'INSERT INTO meal_types (id, name, description, portion_limit, is_active) VALUES (?, ?, ?, ?, ?)',
    [id, name, description, portionLimit, isActive],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      db.get(
        `SELECT 
          id, name, description, portion_limit as portionLimit,
          is_active as isActive, created_at as createdAt
        FROM meal_types WHERE id = ?`,
        [id],
        (err, row) => {
          if (err) {
            return res.status(500).json({ error: 'Database error' });
          }
          res.status(201).json(row);
        }
      );
    }
  );
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// Start server
app.listen(PORT, () => {
  console.log(`KyubiScan server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});