import React, { ReactNode } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useTheme } from '../../hooks/useTheme';
import { LogOut, Moon, Sun, Settings, Scan } from 'lucide-react';
import { cn } from '../../utils/helpers';

interface LayoutProps {
  children: ReactNode;
  title?: string;
  showNav?: boolean;
}

export function Layout({ children, title, showNav = true }: LayoutProps) {
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
      {showNav && (
        <nav className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Scan className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                  <span className="text-xl font-bold text-gray-900 dark:text-white">
                    KyubiScan
                  </span>
                </div>
                {title && (
                  <span className="text-gray-500 dark:text-gray-400">|</span>
                )}
                {title && (
                  <h1 className="text-lg font-medium text-gray-900 dark:text-white">
                    {title}
                  </h1>
                )}
              </div>

              <div className="flex items-center space-x-4">
                <button
                  onClick={toggleTheme}
                  className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  aria-label="Toggle theme"
                >
                  {theme === 'light' ? (
                    <Moon className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                  ) : (
                    <Sun className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                  )}
                </button>

                {user && (
                  <div className="flex items-center space-x-3">
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {user.username}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                        {user.role}
                      </p>
                    </div>
                    <button
                      onClick={logout}
                      className="p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400 transition-colors"
                      aria-label="Logout"
                    >
                      <LogOut className="w-5 h-5" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </nav>
      )}

      <main className={cn(
        'flex-1',
        showNav ? 'pt-0' : 'pt-16'
      )}>
        {children}
      </main>

      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-center items-center text-sm text-gray-500 dark:text-gray-400">
            Powered by KyubiSec &copy; 2025
          </div>
        </div>
      </footer>
    </div>
  );
}