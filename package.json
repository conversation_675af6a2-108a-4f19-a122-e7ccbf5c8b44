{"name": "kyubiscan-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run server\" \"vite\"", "server": "cd server && npm run dev", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "@types/node": "^20.10.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "sqlite3": "^5.1.6", "uuid": "^9.0.1", "qrcode": "^1.5.3", "html5-qrcode": "^2.3.8", "js-cookie": "^3.0.5", "date-fns": "^2.30.0", "clsx": "^2.0.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "concurrently": "^8.2.0", "nodemon": "^3.0.1"}}