import React, { useEffect, useRef, useState } from 'react';
import { Html5QrcodeScanner, Html5QrcodeScannerConfig } from 'html5-qrcode';
import { apiService } from '../../services/apiService';
import { offlineService } from '../../services/offlineService';
import { ScanResult } from '../../types';
import { Button } from '../common/Button';
import { Camera, CameraOff, User, Clock, CheckCircle, XCircle, AlertCircle, Wifi, WifiOff } from 'lucide-react';
import { cn } from '../../utils/helpers';
import { formatDate } from '../../utils/helpers';

export function QRScanner() {
  const scannerRef = useRef<Html5QrcodeScanner | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [unsyncedCount, setUnsyncedCount] = useState(0);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  useEffect(() => {
    updateUnsyncedCount();
  }, []);

  const updateUnsyncedCount = () => {
    setUnsyncedCount(offlineService.getUnsyncedCount());
  };

  const startScanning = () => {
    if (scannerRef.current) {
      scannerRef.current.clear();
    }

    const config: Html5QrcodeScannerConfig = {
      fps: 10,
      qrbox: { width: 250, height: 250 },
      aspectRatio: 1.0,
      showTorchButtonIfSupported: true,
      showZoomSliderIfSupported: true,
      defaultZoomValueIfSupported: 2,
    };

    scannerRef.current = new Html5QrcodeScanner('qr-reader', config, false);
    
    scannerRef.current.render(
      (decodedText) => {
        handleScanSuccess(decodedText);
      },
      (error) => {
        console.log('QR scan error:', error);
      }
    );

    setIsScanning(true);
  };

  const stopScanning = () => {
    if (scannerRef.current) {
      scannerRef.current.clear();
      scannerRef.current = null;
    }
    setIsScanning(false);
  };

  const handleScanSuccess = async (uid: string) => {
    stopScanning();
    
    try {
      if (isOnline) {
        const result = await apiService.scanQRCode(uid);
        setScanResult(result);
        
        if (result.status === 'eligible') {
          // Auto-claim meal for eligible attendees
          const claimResult = await apiService.claimMeal(uid);
          setScanResult(claimResult);
        }
      } else {
        // Offline mode
        offlineService.addOfflineScan(uid);
        setScanResult({
          success: true,
          status: 'eligible',
          message: 'Scan recorded offline. Will sync when online.',
          color: 'green'
        });
        updateUnsyncedCount();
      }
    } catch (error) {
      setScanResult({
        success: false,
        status: 'error',
        message: 'Failed to process scan. Please try again.',
        color: 'red'
      });
    }

    // Clear result after 3 seconds
    setTimeout(() => {
      setScanResult(null);
    }, 3000);
  };

  const syncOfflineScans = async () => {
    if (!isOnline) return;

    try {
      await offlineService.syncOfflineScans();
      updateUnsyncedCount();
      setScanResult({
        success: true,
        status: 'eligible',
        message: 'Offline scans synced successfully!',
        color: 'green'
      });
    } catch (error) {
      setScanResult({
        success: false,
        status: 'error',
        message: 'Failed to sync offline scans.',
        color: 'red'
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'eligible':
        return <CheckCircle className="w-8 h-8 text-green-500" />;
      case 'already_claimed':
        return <XCircle className="w-8 h-8 text-red-500" />;
      case 'not_found':
        return <AlertCircle className="w-8 h-8 text-gray-500" />;
      default:
        return <AlertCircle className="w-8 h-8 text-red-500" />;
    }
  };

  const getStatusColor = (color: string) => {
    switch (color) {
      case 'green':
        return 'bg-green-100 border-green-300 dark:bg-green-900/20 dark:border-green-700';
      case 'red':
        return 'bg-red-100 border-red-300 dark:bg-red-900/20 dark:border-red-700';
      default:
        return 'bg-gray-100 border-gray-300 dark:bg-gray-800 dark:border-gray-600';
    }
  };

  return (
    <div className="max-w-md mx-auto p-4 space-y-6">
      {/* Connection Status */}
      <div className="flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
        <div className="flex items-center space-x-2">
          {isOnline ? (
            <Wifi className="w-5 h-5 text-green-500" />
          ) : (
            <WifiOff className="w-5 h-5 text-red-500" />
          )}
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {isOnline ? 'Online' : 'Offline Mode'}
          </span>
        </div>
        
        {unsyncedCount > 0 && (
          <Button
            size="sm"
            onClick={syncOfflineScans}
            disabled={!isOnline}
            className="text-xs"
          >
            Sync ({unsyncedCount})
          </Button>
        )}
      </div>

      {/* Scanner Area */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
        <div className="p-6">
          <h2 className="text-xl font-bold text-center text-gray-900 dark:text-white mb-6">
            QR Code Scanner
          </h2>
          
          {!isScanning && !scanResult && (
            <div className="text-center space-y-4">
              <div className="w-24 h-24 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <Camera className="w-12 h-12 text-gray-400" />
              </div>
              <p className="text-gray-600 dark:text-gray-400">
                Position QR code within the frame to scan
              </p>
              <Button onClick={startScanning} size="lg" className="w-full">
                Start Scanning
              </Button>
            </div>
          )}

          {isScanning && (
            <div className="space-y-4">
              <div id="qr-reader" className="rounded-lg overflow-hidden"></div>
              <Button
                variant="secondary"
                onClick={stopScanning}
                className="w-full"
              >
                <CameraOff className="w-4 h-4 mr-2" />
                Stop Scanning
              </Button>
            </div>
          )}

          {scanResult && (
            <div className={cn(
              'p-6 rounded-lg border-2 text-center space-y-4',
              getStatusColor(scanResult.color)
            )}>
              <div className="flex justify-center">
                {getStatusIcon(scanResult.status)}
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {scanResult.status === 'eligible' ? 'Meal Served!' :
                   scanResult.status === 'already_claimed' ? 'Already Claimed' :
                   scanResult.status === 'not_found' ? 'Not Found' : 'Error'}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {scanResult.message}
                </p>
              </div>

              {scanResult.attendee && (
                <div className="bg-white dark:bg-gray-700 rounded-lg p-4 space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <User className="w-6 h-6 text-blue-600 dark:text-blue-300" />
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {scanResult.attendee.name}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {scanResult.attendee.email}
                      </p>
                    </div>
                  </div>
                  
                  {scanResult.attendee.claimedAt && (
                    <div className="flex items-center justify-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                      <Clock className="w-4 h-4" />
                      <span>Claimed: {formatDate(scanResult.attendee.claimedAt)}</span>
                    </div>
                  )}
                </div>
              )}

              <Button
                onClick={() => {
                  setScanResult(null);
                  startScanning();
                }}
                className="w-full"
              >
                Scan Next
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}