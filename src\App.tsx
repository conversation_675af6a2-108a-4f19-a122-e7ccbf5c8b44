import React from 'react';
import { useAuth, AuthProvider } from './hooks/useAuth';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { Layout } from './components/common/Layout';
import { Dashboard } from './components/admin/Dashboard';
import { AttendeeManagement } from './components/admin/AttendeeManagement';
import { QRScanner } from './components/scanner/QRScanner';
import { Scan, Users, BarChart3 } from 'lucide-react';
import { useState } from 'react';
import { cn } from './utils/helpers';

function AppContent() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');

  if (!user) {
    return (
      <ProtectedRoute>
        <div />
      </ProtectedRoute>
    );
  }

  // Scanner role only sees the scanner interface
  if (user.role === 'scanner') {
    return (
      <ProtectedRoute requiredRole="scanner">
        <Layout title="Mobile Scanner" showNav={true}>
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-6">
            <QRScanner />
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  // Admin role sees full dashboard
  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3, component: Dashboard },
    { id: 'attendees', label: 'Attendees', icon: Users, component: AttendeeManagement },
    { id: 'scanner', label: 'Scanner', icon: Scan, component: QRScanner }
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || Dashboard;

  return (
    <ProtectedRoute requiredRole="admin">
      <Layout showNav={true}>
        <div className="flex h-[calc(100vh-64px)]">
          {/* Sidebar */}
          <div className="w-64 bg-white dark:bg-gray-800 shadow-sm border-r border-gray-200 dark:border-gray-700">
            <nav className="p-4 space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={cn(
                      'w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors',
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                        : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                    )}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-auto">
            <div className="p-6">
              <ActiveComponent />
            </div>
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;