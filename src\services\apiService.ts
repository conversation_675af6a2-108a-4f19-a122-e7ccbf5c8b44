import { Attendee, MealType, <PERSON>board<PERSON>tats, ScanResult } from '../types';
import { authService } from './authService';

class ApiService {
  private baseUrl = '/api';

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = authService.getAuthToken();
    const url = `${this.baseUrl}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Attendee endpoints
  async getAttendees(): Promise<Attendee[]> {
    return this.request<Attendee[]>('/attendees');
  }

  async createAttendee(attendee: Omit<Attendee, 'id' | 'createdAt'>): Promise<Attendee> {
    return this.request<Attendee>('/attendees', {
      method: 'POST',
      body: JSON.stringify(attendee),
    });
  }

  async updateAttendee(id: string, updates: Partial<Attendee>): Promise<Attendee> {
    return this.request<Attendee>(`/attendees/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deleteAttendee(id: string): Promise<void> {
    return this.request<void>(`/attendees/${id}`, {
      method: 'DELETE',
    });
  }

  // Scanning endpoints
  async scanQRCode(uid: string): Promise<ScanResult> {
    return this.request<ScanResult>('/scan', {
      method: 'POST',
      body: JSON.stringify({ uid }),
    });
  }

  async claimMeal(uid: string): Promise<ScanResult> {
    return this.request<ScanResult>('/scan/claim', {
      method: 'POST',
      body: JSON.stringify({ uid }),
    });
  }

  // Dashboard endpoints
  async getDashboardStats(): Promise<DashboardStats> {
    return this.request<DashboardStats>('/dashboard/stats');
  }

  // Meal type endpoints
  async getMealTypes(): Promise<MealType[]> {
    return this.request<MealType[]>('/meal-types');
  }

  async createMealType(mealType: Omit<MealType, 'id' | 'createdAt'>): Promise<MealType> {
    return this.request<MealType>('/meal-types', {
      method: 'POST',
      body: JSON.stringify(mealType),
    });
  }
}

export const apiService = new ApiService();