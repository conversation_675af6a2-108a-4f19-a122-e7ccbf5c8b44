# KyubiScan - Smart Event Food Management System

A production-ready web application for managing meal distribution at large-scale events with 500+ attendees.

## 🚀 Features

### Core Functionality
- **QR Code Scanning**: Real-time verification with color-coded feedback (Green/Red/White)
- **Role-Based Access**: Admin dashboard and mobile scanner interface
- **Offline Support**: Continue scanning without internet connection
- **Real-Time Statistics**: Live dashboard with meal distribution tracking
- **Attendee Management**: Complete CRUD operations for event attendees

### Security & Authentication
- JWT-based authentication with secure token management
- Role-based access control (Admin/Scanner roles)
- Protected API endpoints with proper authorization
- Input validation and SQL injection prevention

### Mobile-First Design
- Responsive design optimized for mobile devices
- Touch-friendly interface for scanner operators
- Dark/Light theme support with user preference persistence
- Progressive Web App capabilities

## 🛠 Technology Stack

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **HTML5 QRCode** for camera scanning
- **Vite** for development and building

### Backend
- **Node.js** with Express.js
- **SQLite** database for data persistence
- **JWT** for authentication
- **bcrypt** for password hashing
- **CORS** and **Helmet** for security

## 📦 Installation & Setup

### Prerequisites
- Node.js 18+ and npm
- Modern web browser with camera support

### Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd kyubiscan
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm run dev
   ```
   This starts both frontend (http://localhost:5173) and backend (http://localhost:3001) servers.

3. **Access the Application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3001/api

### Demo Credentials

**Admin Account:**
- Username: `admin`
- Password: `admin123`
- Access: Full dashboard, attendee management, scanning

**Scanner Account:**
- Username: `scanner`
- Password: `scanner123`
- Access: Mobile scanner interface only

## 🎯 Usage Guide

### For Administrators

1. **Login** with admin credentials
2. **Dashboard**: View real-time statistics and recent scans
3. **Attendee Management**: Add, edit, delete attendees and generate QR codes
4. **Scanner Access**: Use built-in scanner for testing

### For Scanner Operators

1. **Login** with scanner credentials
2. **Mobile Interface**: Optimized for mobile devices
3. **QR Scanning**: Point camera at QR codes for instant verification
4. **Offline Mode**: Continue scanning when internet is unavailable
5. **Auto-Sync**: Offline scans sync automatically when connection returns

### QR Code System

- **Green**: Attendee eligible - meal can be served
- **Red**: Meal already claimed by this attendee
- **White/Gray**: Attendee UID not found in database

## 📊 API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - User authentication

### Attendee Management
- `GET /api/attendees` - List all attendees
- `POST /api/attendees` - Create new attendee
- `PUT /api/attendees/:id` - Update attendee
- `DELETE /api/attendees/:id` - Delete attendee

### Scanning Operations
- `POST /api/scan` - Verify QR code and check eligibility
- `POST /api/scan/claim` - Claim meal for eligible attendee

### Dashboard Data
- `GET /api/dashboard/stats` - Get real-time statistics

### System Health
- `GET /api/health` - Health check endpoint

## 🔧 Configuration

### Environment Variables
- `PORT`: Server port (default: 3001)
- `JWT_SECRET`: JWT signing secret (default: development key)

### Database
- SQLite database automatically created on first run
- Sample data includes demo attendees and users
- Database file: `server/database.sqlite`

## 🚀 Production Deployment

### Build for Production
```bash
npm run build
```

### Production Considerations
- Set strong `JWT_SECRET` environment variable
- Use HTTPS in production
- Configure proper CORS origins
- Set up database backups
- Monitor server logs and performance
- Consider using PostgreSQL for larger deployments

### Performance Specifications
- **Scalability**: Handles 500+ attendees efficiently
- **Response Time**: Sub-second QR code verification
- **Offline Support**: Local storage with automatic sync
- **Mobile Optimized**: Touch-friendly interface design

## 🔐 Security Features

- Password hashing with bcrypt
- JWT token-based authentication
- Role-based access control
- Input validation and sanitization
- SQL injection prevention
- XSS protection with Helmet.js
- CORS configuration

## 📱 Mobile Features

- Camera integration for QR scanning
- Offline scanning with local storage
- Touch-optimized interface
- Responsive design for all screen sizes
- Progressive Web App capabilities
- Auto-sync when connection restored

## 🎨 Branding

- KyubiSec company branding integration
- Professional color scheme
- Clean, modern interface design
- Consistent visual hierarchy
- Dark/Light theme support

## 📞 Support

For production deployment support or customization requests, contact the development team.

---

**KyubiScan** - Powered by KyubiSec © 2025